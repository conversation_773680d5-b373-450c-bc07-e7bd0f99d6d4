import os
import subprocess
import platform

def play_audio_with_system_player(audio_filename):
    """Play audio file using the system's default media player."""
    # Get the absolute path to the audio file
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), audio_filename)

    print(f"Attempting to play with system player: {file_path}")
    print(f"File exists: {os.path.exists(file_path)}")

    if not os.path.exists(file_path):
        print("Audio file not found!")
        return False

    try:
        # Use the system's default media player
        if platform.system() == "Windows":
            # Use Windows Media Player or default audio player
            subprocess.run(["start", "", file_path], shell=True, check=True)
            print("✅ Audio file opened with default Windows media player!")
            print("The audio should be playing in your default media player.")
            return True
        elif platform.system() == "Darwin":  # macOS
            subprocess.run(["open", file_path], check=True)
            print("✅ Audio file opened with default macOS media player!")
            return True
        elif platform.system() == "Linux":
            subprocess.run(["xdg-open", file_path], check=True)
            print("✅ Audio file opened with default Linux media player!")
            return True
        else:
            print("❌ Unsupported operating system")
            return False

    except subprocess.CalledProcessError as e:
        print(f"System player error: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

def play_audio_with_vlc(audio_filename):
    """Try to play audio file using VLC media player."""
    # Get the absolute path to the audio file
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), audio_filename)

    print(f"Attempting to play with VLC: {file_path}")

    if not os.path.exists(file_path):
        print("Audio file not found!")
        return False

    try:
        # Common VLC installation paths on Windows
        vlc_paths = [
            r"C:\Program Files\VideoLAN\VLC\vlc.exe",
            r"C:\Program Files (x86)\VideoLAN\VLC\vlc.exe",
            "vlc"  # If VLC is in PATH
        ]

        for vlc_path in vlc_paths:
            try:
                subprocess.run([vlc_path, file_path], check=True)
                print("✅ Audio file opened with VLC!")
                return True
            except (subprocess.CalledProcessError, FileNotFoundError):
                continue

        print("❌ VLC not found or failed to start")
        return False

    except Exception as e:
        print(f"VLC error: {e}")
        return False

def play_audio(audio_filename):
    """Try to play audio using the best available method."""
    print(f"\n🎵 Attempting to play: {audio_filename}")

    # Try system default player first
    if play_audio_with_system_player(audio_filename):
        return True

    print("\nSystem player failed, trying VLC...")
    # Try VLC as fallback
    if play_audio_with_vlc(audio_filename):
        return True

    print("\nAll methods failed.")
    return False

# Main execution
if __name__ == "__main__":
    # List of audio files to try (in order of preference)
    AUDIO_FILES = [
        'Assala - Shabeah Rouhi _ Lyrics Video 2023 _ أصالة - شبيه روحي(M4A_128K).m4a'
    ]

    for current_file in AUDIO_FILES:
        print(f"\n--- Trying to play: {current_file} ---")
        if play_audio(current_file):
            print("\n✅ Audio playback completed successfully!")
            break
        print("Failed to play this file, trying next...")
    else:
        # This else clause executes if the loop completes without breaking
        print("\n❌ Could not play any audio files.")
        print("\nTip: For better audio compatibility, consider:")
        print("1. Converting M4A files to MP3 format")
        print("2. Using VLC media player or other dedicated audio players")
        print("3. Installing additional audio codecs")