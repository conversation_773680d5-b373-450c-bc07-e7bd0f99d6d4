
# pylint: disable=no-member
import sys
import cv2
import vlc
import time
import os

def play_audio(video_file):
    """تشغيل الصوت في خيط منفصل باستخدام VLC"""
    try:
        # إنشاء مشغل VLC للصوت فقط
        instance = vlc.Instance('--intf dummy --aout directsound')
        player = instance.media_player_new()
        media = instance.media_new(os.path.abspath(video_file))
        player.set_media(media)

        # تشغيل الصوت
        player.play()
        print("تم بدء تشغيل الصوت...")

        return player
    except Exception as e:
        print(f"خطأ في تشغيل الصوت: {e}")
        return None

# فتح ملف الفيديو
cap = cv2.VideoCapture('Video .mp4')

# التحقق من أن الفيديو تم فتحه بنجاح
if not cap.isOpened():
    print("خطأ: لم يتم فتح ملف الفيديو.")
    sys.exit(1)

# الحصول على معلومات الفيديو
original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
fps = cap.get(cv2.CAP_PROP_FPS)

# تحديد حجم العرض المصغر (50% من الحجم الأصلي)
display_width = original_width // 2
display_height = original_height // 2

# طباعة معلومات الفيديو
print(f"عدد الإطارات: {cap.get(cv2.CAP_PROP_FRAME_COUNT)}")
print(f"معدل الإطارات: {fps}")
print(f"الحجم الأصلي: {original_width}x{original_height}")
print(f"حجم العرض: {display_width}x{display_height}")
print("التحكم:")
print("  - اضغط 'm' أو 'q' للخروج")
print("  - اضغط 'ESC' للخروج")
print("  - اضغط 'SPACE' لإيقاف/تشغيل مؤقت")
print("  - أغلق النافذة للخروج")

# إنشاء نافذة بحجم قابل للتعديل
cv2.namedWindow('Video Player', cv2.WINDOW_NORMAL)
cv2.resizeWindow('Video Player', display_width, display_height)

# بدء تشغيل الصوت
audio_player = play_audio('Video .mp4')

frame_count = 0
start_time = time.time()
paused = False
pause_start_time = 0

try:
    while True:
        if not paused:
            ret, frame = cap.read()

            if not ret:
                print(f"\nانتهى الفيديو بعد {frame_count} إطار.")
                break

            frame_count += 1

            # تصغير حجم الإطار
            resized_frame = cv2.resize(frame, (display_width, display_height))

            # عرض الإطار
            cv2.imshow('Video Player', resized_frame)

            # حساب التوقيت الصحيح للعرض
            expected_time = frame_count / fps
            elapsed_time = time.time() - start_time

            if elapsed_time < expected_time:
                wait_time = int((expected_time - elapsed_time) * 1000)
                wait_time = max(1, min(wait_time, 100))  # بين 1 و 100 مللي ثانية
            else:
                wait_time = 1
        else:
            wait_time = 30  # انتظار أطول عند الإيقاف المؤقت

        key = cv2.waitKey(wait_time) & 0xFF

        if key == ord('m') or key == ord('q') or key == 27:  # m, q, أو ESC
            print(f"\nتم إنهاء العرض بعد {frame_count} إطار.")
            break
        elif key == ord(' '):  # مفتاح المسافة للإيقاف المؤقت
            if not paused:
                paused = True
                pause_start_time = time.time()
                if audio_player:
                    audio_player.pause()
                print("\nتم إيقاف الفيديو مؤقتاً. اضغط مسافة مرة أخرى للمتابعة.")
            else:
                paused = False
                # تعديل وقت البداية لتعويض فترة الإيقاف
                start_time += time.time() - pause_start_time
                if audio_player:
                    audio_player.play()
                print("تم استئناف تشغيل الفيديو.")

        # التحقق من إغلاق النافذة
        if cv2.getWindowProperty('Video Player', cv2.WND_PROP_VISIBLE) < 1:
            print("\nتم إغلاق النافذة.")
            break

finally:
    # تنظيف الموارد
    cap.release()
    cv2.destroyAllWindows()

    # إيقاف تشغيل الصوت
    if audio_player:
        audio_player.stop()
        print("تم إيقاف تشغيل الصوت.")