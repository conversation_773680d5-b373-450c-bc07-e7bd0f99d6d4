
# pylint: disable=no-member
import sys
import cv2

# فتح ملف الفيديو
cap = cv2.VideoCapture('Video .mp4')

# التحقق من أن الفيديو تم فتحه بنجاح
if not cap.isOpened():
    print("خطأ: لم يتم فتح ملف الفيديو.")
    sys.exit(1)

# طباعة معلومات الفيديو
print(f"عدد الإطارات: {cap.get(cv2.CAP_PROP_FRAME_COUNT)}")
print(f"معدل الإطارات: {cap.get(cv2.CAP_PROP_FPS)}")
print(f"العرض: {cap.get(cv2.CAP_PROP_FRAME_WIDTH)}")
print(f"الارتفاع: {cap.get(cv2.CAP_PROP_FRAME_HEIGHT)}")
print("اضغط 'm' للخروج")

frame_count = 0
while True:
    ret, frame = cap.read()

    if not ret:
        print(f"انتهى الفيديو بعد {frame_count} إطار.")
        break

    frame_count += 1
    print(f"عرض الإطار رقم: {frame_count}", end='\r')

    cv2.imshow('Video Frame', frame)

    # انتظار أقل لجعل الفيديو أسرع (25ms = ~40 FPS)
    key = cv2.waitKey(25) & 0xFF
    if key == ord('m'):
        print(f"\nتم الضغط على m. إنهاء العرض بعد {frame_count} إطار.")
        break

cap.release()
cv2.destroyAllWindows()